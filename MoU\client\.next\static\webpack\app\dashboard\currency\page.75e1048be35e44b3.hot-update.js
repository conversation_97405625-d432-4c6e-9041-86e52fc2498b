"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/currency/page",{

/***/ "(app-pages-browser)/./app/dashboard/currency/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/currency/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CurrencyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/master-data.service */ \"(app-pages-browser)/./lib/services/master-data.service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction CurrencyPage() {\n    _s();\n    const [currencies, setCurrencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formLoading, setFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCurrency, setEditingCurrency] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currencyCode, setCurrencyCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currencyName, setCurrencyName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CurrencyPage.useEffect\": ()=>{\n            loadCurrencies();\n        }\n    }[\"CurrencyPage.useEffect\"], []);\n    const loadCurrencies = async ()=>{\n        try {\n            setLoading(true);\n            const data = await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_9__.masterDataService.getCurrencies();\n            setCurrencies(data);\n        } catch (error) {\n            console.error(\"Failed to load currencies:\", error);\n            setError(\"Failed to load currencies\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredCurrencies = currencies.filter((currency)=>currency.currencyCode.toLowerCase().includes(searchTerm.toLowerCase()) || currency.currencyName.toLowerCase().includes(searchTerm.toLowerCase()));\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            if (editingCurrency) {\n                // Update existing currency\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_9__.masterDataService.updateCurrency(editingCurrency.id, {\n                    currencyCode,\n                    currencyName\n                });\n                setSuccess(\"Currency updated successfully\");\n            } else {\n                // Create new currency\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_9__.masterDataService.createCurrency({\n                    currencyCode,\n                    currencyName\n                });\n                setSuccess(\"Currency created successfully\");\n            }\n            // Reload the data to get the latest from server\n            await loadCurrencies();\n            setDialogOpen(false);\n            setCurrencyCode(\"\");\n            setCurrencyName(\"\");\n            setEditingCurrency(null);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Failed to save currency:\", err);\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"Failed to save currency\");\n        } finally{\n            setFormLoading(false);\n        }\n    };\n    const handleEdit = (currency)=>{\n        setEditingCurrency(currency);\n        setCurrencyCode(currency.currencyCode);\n        setCurrencyName(currency.currencyName);\n        setDialogOpen(true);\n    };\n    const handleDelete = async (id)=>{\n        if (confirm(\"Are you sure you want to delete this currency?\")) {\n            try {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_9__.masterDataService.deleteCurrency(id);\n                setSuccess(\"Currency deleted successfully\");\n                // Reload the data to get the latest from server\n                await loadCurrencies();\n            } catch (err) {\n                var _err_response_data, _err_response;\n                console.error(\"Failed to delete currency:\", err);\n                setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"Failed to delete currency\");\n            }\n        }\n    };\n    const resetForm = ()=>{\n        setCurrencyCode(\"\");\n        setCurrencyName(\"\");\n        setEditingCurrency(null);\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Currencies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                        open: dialogOpen,\n                        onOpenChange: (open)=>{\n                            setDialogOpen(open);\n                            if (!open) resetForm();\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Add Currency\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                                    children: editingCurrency ? \"Edit Currency\" : \"Add Currency\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                                    children: editingCurrency ? \"Update the currency details.\" : \"Create a new currency.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 py-4\",\n                                            children: [\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                                    variant: \"destructive\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"currencyCode\",\n                                                            children: \"Currency Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"currencyCode\",\n                                                            value: currencyCode,\n                                                            onChange: (e)=>setCurrencyCode(e.target.value.toUpperCase()),\n                                                            placeholder: \"Enter currency code (e.g., USD, EUR)\",\n                                                            maxLength: 3,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"currencyName\",\n                                                            children: \"Currency Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"currencyName\",\n                                                            value: currencyName,\n                                                            onChange: (e)=>setCurrencyName(e.target.value),\n                                                            placeholder: \"Enter currency name (e.g., US Dollar, Euro)\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                disabled: formLoading,\n                                                children: formLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        editingCurrency ? \"Updating...\" : \"Creating...\"\n                                                    ]\n                                                }, void 0, true) : editingCurrency ? \"Update\" : \"Create\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                className: \"bg-green-50 text-green-700 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            placeholder: \"Search currencies...\",\n                            className: \"pl-8\",\n                            value: searchTerm,\n                            onChange: (e)=>setSearchTerm(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-md border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                        children: \"Currency Code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                        children: \"Currency Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                        children: \"Created At\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                        children: \"Updated At\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                        className: \"w-[50px]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                    colSpan: 5,\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 animate-spin mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-muted-foreground\",\n                                            children: \"Loading currencies...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this) : filteredCurrencies.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                    colSpan: 5,\n                                    className: \"text-center py-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"No currencies found matching your search.\" : \"No currencies found.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, this) : filteredCurrencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                            className: \"font-medium font-mono\",\n                                            children: currency.currencyCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                            children: currency.currencyName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                            children: new Date(currency.createdAt).toLocaleDateString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                            children: new Date(currency.updatedAt).toLocaleDateString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"h-8 w-8 p-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Open menu\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuContent, {\n                                                        align: \"end\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuLabel, {\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                                onClick: ()=>handleEdit(currency),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Edit\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                                onClick: ()=>handleDelete(currency.id),\n                                                                className: \"text-red-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Delete\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, currency.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\currency\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyPage, \"jDp7VanbRBWju1rviwiwDPIkyJo=\");\n_c = CurrencyPage;\nvar _c;\n$RefreshReg$(_c, \"CurrencyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/currency/page.tsx\n"));

/***/ })

});