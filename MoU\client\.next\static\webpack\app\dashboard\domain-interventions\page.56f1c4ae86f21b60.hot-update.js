"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain-interventions/page",{

/***/ "(app-pages-browser)/./app/dashboard/domain-interventions/page.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/domain-interventions/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DomainInterventionsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _components_ui_tree_view__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tree-view */ \"(app-pages-browser)/./components/ui/tree-view.tsx\");\n/* harmony import */ var _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/master-data.service */ \"(app-pages-browser)/./lib/services/master-data.service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DomainInterventionsPage() {\n    _s();\n    const [domains, setDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [treeData, setTreeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formLoading, setFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingDomain, setEditingDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [parentDomain, setParentDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Form state\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedParentId, setSelectedParentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DomainInterventionsPage.useEffect\": ()=>{\n            loadDomains();\n        }\n    }[\"DomainInterventionsPage.useEffect\"], []);\n    const loadDomains = async ()=>{\n        try {\n            setLoading(true);\n            const [domainsData, treeData] = await Promise.all([\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.getDomainInterventions(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.getDomainInterventionsTree()\n            ]);\n            setDomains(domainsData);\n            setTreeData(treeData);\n        } catch (error) {\n            console.error(\"Failed to load domain interventions:\", error);\n            setError(\"Failed to load domain interventions\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetForm = ()=>{\n        setDomainName(\"\");\n        setDescription(\"\");\n        setSelectedParentId(\"none\");\n        setEditingDomain(null);\n        setParentDomain(null);\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormLoading(true);\n        setError(\"\");\n        try {\n            const domainData = {\n                domainName,\n                description: description || undefined,\n                parentId: selectedParentId && selectedParentId !== \"none\" ? parseInt(selectedParentId) : undefined\n            };\n            if (editingDomain) {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.updateDomainIntervention(editingDomain.id, domainData);\n                setSuccess(\"Domain intervention updated successfully\");\n            } else {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.createDomainIntervention(domainData);\n                setSuccess(\"Domain intervention created successfully\");\n            }\n            await loadDomains();\n            setDialogOpen(false);\n            resetForm();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to save domain intervention\");\n        } finally{\n            setFormLoading(false);\n        }\n    };\n    const handleEdit = (node)=>{\n        const domain = domains.find((d)=>d.id === node.id);\n        if (domain) {\n            setEditingDomain(domain);\n            setDomainName(domain.domainName);\n            setDescription(domain.description || \"\");\n            setSelectedParentId(domain.parentId ? domain.parentId.toString() : \"none\");\n            setDialogOpen(true);\n        }\n    };\n    const handleDelete = async (node)=>{\n        if (confirm(\"Are you sure you want to delete this domain intervention?\")) {\n            try {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.deleteDomainIntervention(node.id);\n                setSuccess(\"Domain intervention deleted successfully\");\n                await loadDomains();\n            } catch (error) {\n                var _error_response_data, _error_response;\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete domain intervention\");\n            }\n        }\n    };\n    const handleAddChild = (parentNode)=>{\n        const parent = domains.find((d)=>d.id === parentNode.id);\n        if (parent) {\n            setParentDomain(parent);\n            setSelectedParentId(parent.id.toString());\n            setDialogOpen(true);\n        }\n    };\n    const openCreateDialog = ()=>{\n        resetForm();\n        setDialogOpen(true);\n    };\n    // Convert domains to tree nodes\n    const convertToTreeNodes = (domains)=>{\n        return domains.map((domain)=>({\n                id: domain.id,\n                name: domain.domainName,\n                description: domain.description,\n                parentId: domain.parentId,\n                children: domain.children ? convertToTreeNodes(domain.children) : []\n            }));\n    };\n    // Filter tree data based on search term\n    const filterTreeData = (nodes, searchTerm)=>{\n        if (!searchTerm) return nodes;\n        const filtered = [];\n        const searchInNode = (node)=>{\n            const matchesSearch = node.domainName.toLowerCase().includes(searchTerm.toLowerCase()) || node.description && node.description.toLowerCase().includes(searchTerm.toLowerCase());\n            const filteredChildren = node.children ? filterTreeData(node.children, searchTerm) : [];\n            const hasMatchingChildren = filteredChildren.length > 0;\n            if (matchesSearch || hasMatchingChildren) {\n                filtered.push({\n                    ...node,\n                    children: filteredChildren\n                });\n                return true;\n            }\n            return false;\n        };\n        nodes.forEach(searchInNode);\n        return filtered;\n    };\n    const filteredTreeData = filterTreeData(treeData, searchTerm);\n    const treeNodes = convertToTreeNodes(filteredTreeData);\n    // Calculate statistics\n    const getMaxDepth = function(nodes) {\n        let currentDepth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (!nodes || nodes.length === 0) return currentDepth;\n        return Math.max(...nodes.map((node)=>getMaxDepth(node.children || [], currentDepth + 1)));\n    };\n    const getTotalNodes = (nodes)=>{\n        return nodes.reduce((total, node)=>total + 1 + getTotalNodes(node.children || []), 0);\n    };\n    const maxDepth = getMaxDepth(treeNodes);\n    const totalNodes = getTotalNodes(treeNodes);\n    const totalFilteredNodes = searchTerm ? getTotalNodes(treeNodes) : domains.length;\n    // Expand/Collapse all functionality\n    const expandAll = ()=>{\n        const getAllNodeIds = (nodes)=>{\n            const ids = [];\n            nodes.forEach((node)=>{\n                ids.push(node.id);\n                if (node.children) {\n                    ids.push(...getAllNodeIds(node.children));\n                }\n            });\n            return ids;\n        };\n        setExpandedNodes(new Set(getAllNodeIds(treeNodes)));\n    };\n    const collapseAll = ()=>{\n        setExpandedNodes(new Set());\n    };\n    // Filter domains for parent selection (exclude current domain and its descendants)\n    const getSelectableParents = ()=>{\n        if (!editingDomain) return domains // All domains available for new items\n        ;\n        const excludeIds = new Set();\n        // Add current domain\n        excludeIds.add(editingDomain.id);\n        // Add all descendants recursively\n        const addDescendants = (parentId)=>{\n            domains.filter((d)=>d.parentId === parentId).forEach((child)=>{\n                excludeIds.add(child.id);\n                addDescendants(child.id);\n            });\n        };\n        addDescendants(editingDomain.id);\n        return domains.filter((d)=>!excludeIds.has(d.id));\n    };\n    // Create hierarchical display names for parent selection\n    const getHierarchicalDisplayName = (domain)=>{\n        const getPath = (d)=>{\n            if (!d.parentId) return [\n                d.domainName\n            ];\n            const parent = domains.find((p)=>p.id === d.parentId);\n            if (!parent) return [\n                d.domainName\n            ];\n            return [\n                ...getPath(parent),\n                d.domainName\n            ];\n        };\n        const path = getPath(domain);\n        return path.length > 1 ? path.join(' → ') : path[0];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Domain Interventions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: dialogOpen,\n                        onOpenChange: setDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: openCreateDialog,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Add Domain Intervention\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                className: \"sm:max-w-[500px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                children: editingDomain ? \"Edit Domain Intervention\" : parentDomain ? 'Add Sub-Domain to \"'.concat(parentDomain.domainName, '\"') : \"Create New Domain Intervention\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                children: editingDomain ? \"Update domain intervention information.\" : \"Add a new domain intervention to the system.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4 py-4\",\n                                                children: [\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                                        variant: \"destructive\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"domainName\",\n                                                                children: \"Domain Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"domainName\",\n                                                                value: domainName,\n                                                                onChange: (e)=>setDomainName(e.target.value),\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"description\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                id: \"description\",\n                                                                value: description,\n                                                                onChange: (e)=>setDescription(e.target.value),\n                                                                rows: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"parentId\",\n                                                                children: \"Parent Domain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                value: selectedParentId,\n                                                                onValueChange: setSelectedParentId,\n                                                                disabled: !!parentDomain,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                            placeholder: parentDomain ? parentDomain.domainName : \"Select parent domain (optional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                value: \"none\",\n                                                                                children: \"No Parent (Root Domain)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            getSelectableParents().sort((a, b)=>{\n                                                                                // Sort by hierarchy depth first, then by name\n                                                                                const aDepth = getHierarchicalDisplayName(a).split(' → ').length;\n                                                                                const bDepth = getHierarchicalDisplayName(b).split(' → ').length;\n                                                                                if (aDepth !== bDepth) return aDepth - bDepth;\n                                                                                return getHierarchicalDisplayName(a).localeCompare(getHierarchicalDisplayName(b));\n                                                                            }).map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: domain.id.toString(),\n                                                                                    children: getHierarchicalDisplayName(domain)\n                                                                                }, domain.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                                    lineNumber: 322,\n                                                                                    columnNumber: 27\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"submit\",\n                                                    disabled: formLoading,\n                                                    children: formLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            editingDomain ? \"Updating...\" : \"Creating...\"\n                                                        ]\n                                                    }, void 0, true) : editingDomain ? \"Update Domain\" : \"Create Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                className: \"bg-green-50 text-green-700 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 350,\n                columnNumber: 9\n            }, this),\n            error && !dialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                placeholder: \"Search domain interventions...\",\n                                className: \"pl-8\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: expandAll,\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Expand All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: collapseAll,\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Collapse All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Domain Hierarchy\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                children: \"Manage domain interventions with unlimited nesting. Create complex hierarchical structures for organizing interventions.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 text-sm text-muted-foreground mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Total: \",\n                                            searchTerm ? totalFilteredNodes : domains.length,\n                                            \" domains\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, this),\n                                    maxDepth > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Max depth: \",\n                                            maxDepth,\n                                            \" levels\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 34\n                                    }, this),\n                                    searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Showing: \",\n                                            totalNodes,\n                                            \" matches\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tree_view__WEBPACK_IMPORTED_MODULE_10__.TreeView, {\n                            data: treeNodes,\n                            onEdit: handleEdit,\n                            onDelete: handleDelete,\n                            onAddChild: handleAddChild,\n                            nameKey: \"name\",\n                            className: \"border rounded-lg p-4 bg-gray-50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 398,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainInterventionsPage, \"p6u1WhUCwVO+L1DDZQ8iEpy9td8=\");\n_c = DomainInterventionsPage;\nvar _c;\n$RefreshReg$(_c, \"DomainInterventionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/domain-interventions/page.tsx\n"));

/***/ })

});