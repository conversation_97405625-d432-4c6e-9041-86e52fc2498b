"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/input-categories/page",{

/***/ "(app-pages-browser)/./components/ui/tree-view.tsx":
/*!*************************************!*\
  !*** ./components/ui/tree-view.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeView: () => (/* binding */ TreeView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ TreeView auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst TreeNodeComponent = (param)=>{\n    let { node, level, onEdit, onDelete, onAddChild, nameKey, isExpanded, onToggleExpand } = param;\n    var _node_children;\n    _s();\n    const hasChildren = node.children && node.children.length > 0;\n    const handleToggle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TreeNodeComponent.useCallback[handleToggle]\": ()=>{\n            if (hasChildren) {\n                onToggleExpand(node.id);\n            }\n        }\n    }[\"TreeNodeComponent.useCallback[handleToggle]\"], [\n        hasChildren,\n        node.id,\n        onToggleExpand\n    ]);\n    const paddingLeft = level * 20;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"select-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center py-2 px-2 hover:bg-gray-50 rounded-md group relative transition-colors duration-150\",\n                style: {\n                    paddingLeft: \"\".concat(paddingLeft, \"px\")\n                },\n                children: [\n                    level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 bottom-0 flex\",\n                        children: [\n                            Array.from({\n                                length: level\n                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 flex justify-center\",\n                                    style: {\n                                        left: \"\".concat(i * 20, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-px bg-gray-200 h-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, i, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-6 border-l border-b border-gray-200 rounded-bl\",\n                                style: {\n                                    left: \"\".concat((level - 1) * 20, \"px\"),\n                                    top: '50%',\n                                    transform: 'translateY(-50%)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleToggle,\n                                className: \"flex items-center justify-center w-6 h-6 mr-2 hover:bg-gray-200 rounded transition-colors duration-150\",\n                                disabled: !hasChildren,\n                                children: hasChildren ? isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mr-2\",\n                                children: hasChildren ? isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 bg-gray-300 rounded-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-sm truncate\",\n                                                children: node[nameKey] || node.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs px-1.5 py-0.5\",\n                                                        children: [\n                                                            \"L\",\n                                                            level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-xs px-1.5 py-0.5\",\n                                                        children: ((_node_children = node.children) === null || _node_children === void 0 ? void 0 : _node_children.length) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 truncate mt-0.5\",\n                                        children: node.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150\",\n                        children: [\n                            onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 w-7 p-0 hover:bg-blue-100\",\n                                onClick: ()=>onEdit(node),\n                                title: \"Edit\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-3 h-3 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined),\n                            onAddChild && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 w-7 p-0 hover:bg-green-100\",\n                                onClick: ()=>onAddChild(node),\n                                title: \"Add Child\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-3 h-3 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"h-7 w-7 p-0 hover:bg-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"More actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-3 h-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-48\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                                                children: [\n                                                    'Actions for \"',\n                                                    node[nameKey] || node.name,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>onEdit(node),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Edit Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            onAddChild && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>onAddChild(node),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Sub-Domain\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: handleToggle,\n                                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Collapse\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Expand\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                        onClick: ()=>onDelete(node),\n                                                        className: \"text-red-600 focus:text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-2 border-l border-gray-200\",\n                children: node.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: index === node.children.length - 1 ? \"border-l-transparent\" : \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TreeNodeComponent, {\n                            node: child,\n                            level: level + 1,\n                            onEdit: onEdit,\n                            onDelete: onDelete,\n                            onAddChild: onAddChild,\n                            nameKey: nameKey,\n                            isExpanded: false,\n                            onToggleExpand: onToggleExpand\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, undefined)\n                    }, child.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TreeNodeComponent, \"ZlkaYGR3P75MlG1Y6zRKbNQ3VWg=\");\n_c = TreeNodeComponent;\nconst TreeView = (param)=>{\n    let { data, onEdit, onDelete, onAddChild, nameKey = \"name\", className = \"\", defaultExpandAll = true, expandedNodes: controlledExpandedNodes, onExpandedChange } = param;\n    _s1();\n    // Internal state for expanded nodes\n    const [internalExpandedNodes, setInternalExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"TreeView.useState\": ()=>{\n            if (defaultExpandAll) {\n                const getAllNodeIds = {\n                    \"TreeView.useState.getAllNodeIds\": (nodes)=>{\n                        const ids = [];\n                        nodes.forEach({\n                            \"TreeView.useState.getAllNodeIds\": (node)=>{\n                                ids.push(node.id);\n                                if (node.children) {\n                                    ids.push(...getAllNodeIds(node.children));\n                                }\n                            }\n                        }[\"TreeView.useState.getAllNodeIds\"]);\n                        return ids;\n                    }\n                }[\"TreeView.useState.getAllNodeIds\"];\n                return new Set(getAllNodeIds(data));\n            }\n            return new Set();\n        }\n    }[\"TreeView.useState\"]);\n    // Use controlled or internal state\n    const expandedNodes = controlledExpandedNodes || internalExpandedNodes;\n    const setExpandedNodes = onExpandedChange || setInternalExpandedNodes;\n    const handleToggleExpand = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TreeView.useCallback[handleToggleExpand]\": (nodeId)=>{\n            const newExpandedNodes = new Set(expandedNodes);\n            if (newExpandedNodes.has(nodeId)) {\n                newExpandedNodes.delete(nodeId);\n            } else {\n                newExpandedNodes.add(nodeId);\n            }\n            setExpandedNodes(newExpandedNodes);\n        }\n    }[\"TreeView.useCallback[handleToggleExpand]\"], [\n        expandedNodes,\n        setExpandedNodes\n    ]);\n    // Memoize the tree rendering for performance\n    const renderTree = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TreeView.useMemo[renderTree]\": ()=>{\n            if (!data || data.length === 0) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-gray-500 \".concat(className),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-8 h-8 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"No items found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, undefined);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1 \".concat(className),\n                children: data.map({\n                    \"TreeView.useMemo[renderTree]\": (node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TreeNodeComponent, {\n                            node: node,\n                            level: 0,\n                            onEdit: onEdit,\n                            onDelete: onDelete,\n                            onAddChild: onAddChild,\n                            nameKey: nameKey,\n                            isExpanded: expandedNodes.has(node.id),\n                            onToggleExpand: handleToggleExpand\n                        }, node.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined)\n                }[\"TreeView.useMemo[renderTree]\"])\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, undefined);\n        }\n    }[\"TreeView.useMemo[renderTree]\"], [\n        data,\n        className,\n        onEdit,\n        onDelete,\n        onAddChild,\n        nameKey,\n        expandedNodes,\n        handleToggleExpand\n    ]);\n    return renderTree;\n};\n_s1(TreeView, \"LlpYBUQ0eEmXQUIO+BH+fs+Znhs=\");\n_c1 = TreeView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TreeNodeComponent\");\n$RefreshReg$(_c1, \"TreeView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/tree-view.tsx\n"));

/***/ })

});