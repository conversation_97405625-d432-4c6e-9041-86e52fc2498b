"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain-interventions/page",{

/***/ "(app-pages-browser)/./app/dashboard/domain-interventions/page.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/domain-interventions/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DomainInterventionsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _components_ui_tree_view__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tree-view */ \"(app-pages-browser)/./components/ui/tree-view.tsx\");\n/* harmony import */ var _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/master-data.service */ \"(app-pages-browser)/./lib/services/master-data.service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DomainInterventionsPage() {\n    _s();\n    const [domains, setDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [treeData, setTreeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formLoading, setFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingDomain, setEditingDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [parentDomain, setParentDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Form state\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedParentId, setSelectedParentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DomainInterventionsPage.useEffect\": ()=>{\n            loadDomains();\n        }\n    }[\"DomainInterventionsPage.useEffect\"], []);\n    const loadDomains = async ()=>{\n        try {\n            setLoading(true);\n            const [domainsData, treeData] = await Promise.all([\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.getDomainInterventions(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.getDomainInterventionsTree()\n            ]);\n            setDomains(domainsData);\n            setTreeData(treeData);\n        } catch (error) {\n            console.error(\"Failed to load domain interventions:\", error);\n            setError(\"Failed to load domain interventions\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetForm = ()=>{\n        setDomainName(\"\");\n        setDescription(\"\");\n        setSelectedParentId(\"none\");\n        setEditingDomain(null);\n        setParentDomain(null);\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormLoading(true);\n        setError(\"\");\n        try {\n            const domainData = {\n                domainName,\n                description: description || undefined,\n                parentId: selectedParentId && selectedParentId !== \"none\" ? parseInt(selectedParentId) : undefined\n            };\n            if (editingDomain) {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.updateDomainIntervention(editingDomain.id, domainData);\n                setSuccess(\"Domain intervention updated successfully\");\n            } else {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.createDomainIntervention(domainData);\n                setSuccess(\"Domain intervention created successfully\");\n            }\n            await loadDomains();\n            setDialogOpen(false);\n            resetForm();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to save domain intervention\");\n        } finally{\n            setFormLoading(false);\n        }\n    };\n    const handleEdit = (node)=>{\n        const domain = domains.find((d)=>d.id === node.id);\n        if (domain) {\n            setEditingDomain(domain);\n            setDomainName(domain.domainName);\n            setDescription(domain.description || \"\");\n            setSelectedParentId(domain.parentId ? domain.parentId.toString() : \"none\");\n            setDialogOpen(true);\n        }\n    };\n    const handleDelete = async (node)=>{\n        if (confirm(\"Are you sure you want to delete this domain intervention?\")) {\n            try {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.deleteDomainIntervention(node.id);\n                setSuccess(\"Domain intervention deleted successfully\");\n                await loadDomains();\n            } catch (error) {\n                var _error_response_data, _error_response;\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete domain intervention\");\n            }\n        }\n    };\n    const handleAddChild = (parentNode)=>{\n        const parent = domains.find((d)=>d.id === parentNode.id);\n        if (parent) {\n            setParentDomain(parent);\n            setSelectedParentId(parent.id.toString());\n            setDialogOpen(true);\n        }\n    };\n    const openCreateDialog = ()=>{\n        resetForm();\n        setDialogOpen(true);\n    };\n    // Convert domains to tree nodes\n    const convertToTreeNodes = (domains)=>{\n        return domains.map((domain)=>({\n                id: domain.id,\n                name: domain.domainName,\n                description: domain.description,\n                parentId: domain.parentId,\n                children: domain.children ? convertToTreeNodes(domain.children) : []\n            }));\n    };\n    // Filter tree data based on search term\n    const filterTreeData = (nodes, searchTerm)=>{\n        if (!searchTerm) return nodes;\n        const filtered = [];\n        const searchInNode = (node)=>{\n            const matchesSearch = node.domainName.toLowerCase().includes(searchTerm.toLowerCase()) || node.description && node.description.toLowerCase().includes(searchTerm.toLowerCase());\n            const filteredChildren = node.children ? filterTreeData(node.children, searchTerm) : [];\n            const hasMatchingChildren = filteredChildren.length > 0;\n            if (matchesSearch || hasMatchingChildren) {\n                filtered.push({\n                    ...node,\n                    children: filteredChildren\n                });\n                return true;\n            }\n            return false;\n        };\n        nodes.forEach(searchInNode);\n        return filtered;\n    };\n    const filteredTreeData = filterTreeData(treeData, searchTerm);\n    const treeNodes = convertToTreeNodes(filteredTreeData);\n    // Calculate statistics\n    const getMaxDepth = function(nodes) {\n        let currentDepth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (!nodes || nodes.length === 0) return currentDepth;\n        return Math.max(...nodes.map((node)=>getMaxDepth(node.children || [], currentDepth + 1)));\n    };\n    const getTotalNodes = (nodes)=>{\n        return nodes.reduce((total, node)=>total + 1 + getTotalNodes(node.children || []), 0);\n    };\n    const maxDepth = getMaxDepth(treeNodes);\n    const totalNodes = getTotalNodes(treeNodes);\n    const totalFilteredNodes = searchTerm ? getTotalNodes(treeNodes) : domains.length;\n    // Filter domains for parent selection (exclude current domain and its descendants)\n    const getSelectableParents = ()=>{\n        if (!editingDomain) return domains // All domains available for new items\n        ;\n        const excludeIds = new Set();\n        // Add current domain\n        excludeIds.add(editingDomain.id);\n        // Add all descendants recursively\n        const addDescendants = (parentId)=>{\n            domains.filter((d)=>d.parentId === parentId).forEach((child)=>{\n                excludeIds.add(child.id);\n                addDescendants(child.id);\n            });\n        };\n        addDescendants(editingDomain.id);\n        return domains.filter((d)=>!excludeIds.has(d.id));\n    };\n    // Create hierarchical display names for parent selection\n    const getHierarchicalDisplayName = (domain)=>{\n        const getPath = (d)=>{\n            if (!d.parentId) return [\n                d.domainName\n            ];\n            const parent = domains.find((p)=>p.id === d.parentId);\n            if (!parent) return [\n                d.domainName\n            ];\n            return [\n                ...getPath(parent),\n                d.domainName\n            ];\n        };\n        const path = getPath(domain);\n        return path.length > 1 ? path.join(' → ') : path[0];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Domain Interventions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: dialogOpen,\n                        onOpenChange: setDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: openCreateDialog,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Add Domain Intervention\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                className: \"sm:max-w-[500px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                children: editingDomain ? \"Edit Domain Intervention\" : parentDomain ? 'Add Sub-Domain to \"'.concat(parentDomain.domainName, '\"') : \"Create New Domain Intervention\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                children: editingDomain ? \"Update domain intervention information.\" : \"Add a new domain intervention to the system.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4 py-4\",\n                                                children: [\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                                        variant: \"destructive\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"domainName\",\n                                                                children: \"Domain Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"domainName\",\n                                                                value: domainName,\n                                                                onChange: (e)=>setDomainName(e.target.value),\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"description\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                id: \"description\",\n                                                                value: description,\n                                                                onChange: (e)=>setDescription(e.target.value),\n                                                                rows: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"parentId\",\n                                                                children: \"Parent Domain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                value: selectedParentId,\n                                                                onValueChange: setSelectedParentId,\n                                                                disabled: !!parentDomain,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                            placeholder: parentDomain ? parentDomain.domainName : \"Select parent domain (optional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                value: \"none\",\n                                                                                children: \"No Parent (Root Domain)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            getSelectableParents().sort((a, b)=>{\n                                                                                // Sort by hierarchy depth first, then by name\n                                                                                const aDepth = getHierarchicalDisplayName(a).split(' → ').length;\n                                                                                const bDepth = getHierarchicalDisplayName(b).split(' → ').length;\n                                                                                if (aDepth !== bDepth) return aDepth - bDepth;\n                                                                                return getHierarchicalDisplayName(a).localeCompare(getHierarchicalDisplayName(b));\n                                                                            }).map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: domain.id.toString(),\n                                                                                    children: getHierarchicalDisplayName(domain)\n                                                                                }, domain.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                                    lineNumber: 302,\n                                                                                    columnNumber: 27\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"submit\",\n                                                    disabled: formLoading,\n                                                    children: formLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            editingDomain ? \"Updating...\" : \"Creating...\"\n                                                        ]\n                                                    }, void 0, true) : editingDomain ? \"Update Domain\" : \"Create Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                className: \"bg-green-50 text-green-700 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this),\n            error && !dialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            placeholder: \"Search domain interventions...\",\n                            className: \"pl-8\",\n                            value: searchTerm,\n                            onChange: (e)=>setSearchTerm(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Domain Hierarchy\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                children: \"Manage domain interventions with unlimited nesting. Create complex hierarchical structures for organizing interventions.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this),\n                            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 text-sm text-muted-foreground mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Total: \",\n                                            searchTerm ? totalFilteredNodes : domains.length,\n                                            \" domains\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, this),\n                                    maxDepth > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Max depth: \",\n                                            maxDepth,\n                                            \" levels\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 34\n                                    }, this),\n                                    searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Showing: \",\n                                            totalNodes,\n                                            \" matches\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tree_view__WEBPACK_IMPORTED_MODULE_10__.TreeView, {\n                            data: treeNodes,\n                            onEdit: handleEdit,\n                            onDelete: handleDelete,\n                            onAddChild: handleAddChild,\n                            nameKey: \"name\",\n                            className: \"border rounded-lg p-4 bg-gray-50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 358,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainInterventionsPage, \"oAgEF7oYKX9rIUwmBtWr87ns+R0=\");\n_c = DomainInterventionsPage;\nvar _c;\n$RefreshReg$(_c, \"DomainInterventionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/domain-interventions/page.tsx\n"));

/***/ })

});