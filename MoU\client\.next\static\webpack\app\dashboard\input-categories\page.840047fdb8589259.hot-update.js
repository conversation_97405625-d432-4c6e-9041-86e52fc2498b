"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/input-categories/page",{

/***/ "(app-pages-browser)/./components/ui/tree-view.tsx":
/*!*************************************!*\
  !*** ./components/ui/tree-view.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeView: () => (/* binding */ TreeView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ TreeView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst TreeNodeComponent = (param)=>{\n    let { node, level, onEdit, onDelete, onAddChild, nameKey, isExpanded, onToggleExpand } = param;\n    var _node_children;\n    _s();\n    const hasChildren = node.children && node.children.length > 0;\n    const handleToggle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TreeNodeComponent.useCallback[handleToggle]\": ()=>{\n            if (hasChildren) {\n                onToggleExpand(node.id);\n            }\n        }\n    }[\"TreeNodeComponent.useCallback[handleToggle]\"], [\n        hasChildren,\n        node.id,\n        onToggleExpand\n    ]);\n    const paddingLeft = level * 20;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"select-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center py-2 px-2 hover:bg-gray-50 rounded-md group relative transition-colors duration-150\",\n                style: {\n                    paddingLeft: \"\".concat(paddingLeft, \"px\")\n                },\n                children: [\n                    level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 bottom-0 flex\",\n                        children: [\n                            Array.from({\n                                length: level\n                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 flex justify-center\",\n                                    style: {\n                                        left: \"\".concat(i * 20, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-px bg-gray-200 h-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, i, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-6 border-l border-b border-gray-200 rounded-bl\",\n                                style: {\n                                    left: \"\".concat((level - 1) * 20, \"px\"),\n                                    top: '50%',\n                                    transform: 'translateY(-50%)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleToggle,\n                                className: \"flex items-center justify-center w-6 h-6 mr-2 hover:bg-gray-200 rounded transition-colors duration-150\",\n                                disabled: !hasChildren,\n                                children: hasChildren ? isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mr-2\",\n                                children: hasChildren ? isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 bg-gray-300 rounded-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-sm truncate\",\n                                                children: node[nameKey] || node.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs px-1.5 py-0.5\",\n                                                        children: [\n                                                            \"L\",\n                                                            level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-xs px-1.5 py-0.5\",\n                                                        children: ((_node_children = node.children) === null || _node_children === void 0 ? void 0 : _node_children.length) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 truncate mt-0.5\",\n                                        children: node.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150\",\n                        children: [\n                            onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 w-7 p-0 hover:bg-blue-100\",\n                                onClick: ()=>onEdit(node),\n                                title: \"Edit\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-3 h-3 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined),\n                            onAddChild && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 w-7 p-0 hover:bg-green-100\",\n                                onClick: ()=>onAddChild(node),\n                                title: \"Add Child\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-3 h-3 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"h-7 w-7 p-0 hover:bg-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"More actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-3 h-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-48\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                                                children: [\n                                                    'Actions for \"',\n                                                    node[nameKey] || node.name,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>onEdit(node),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Edit Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            onAddChild && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>onAddChild(node),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Sub-Domain\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: handleToggle,\n                                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Collapse\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Expand\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                        onClick: ()=>onDelete(node),\n                                                        className: \"text-red-600 focus:text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-2 border-l border-gray-200\",\n                children: node.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TreeNodeComponent, {\n                        node: child,\n                        level: level + 1,\n                        onEdit: onEdit,\n                        onDelete: onDelete,\n                        onAddChild: onAddChild,\n                        nameKey: nameKey,\n                        isExpanded: isExpanded,\n                        onToggleExpand: onToggleExpand\n                    }, child.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TreeNodeComponent, \"ZlkaYGR3P75MlG1Y6zRKbNQ3VWg=\");\n_c = TreeNodeComponent;\nconst TreeView = (param)=>{\n    let { data, onEdit, onDelete, onAddChild, nameKey = \"name\", className = \"\" } = param;\n    if (!data || data.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-gray-500 \".concat(className),\n            children: \"No items found\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n            lineNumber: 264,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1 \".concat(className),\n        children: data.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TreeNodeComponent, {\n                node: node,\n                level: 0,\n                onEdit: onEdit,\n                onDelete: onDelete,\n                onAddChild: onAddChild,\n                nameKey: nameKey\n            }, node.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TreeView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TreeNodeComponent\");\n$RefreshReg$(_c1, \"TreeView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/tree-view.tsx\n"));

/***/ })

});