"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DataTable } from "@/components/data-table"
import { Loader2, Plus } from "lucide-react"

// Mock data for funding sources
const mockFundingSources = [
  { id: 1, sourceName: "World Bank", createAt: "2023-01-15", updatedAt: "2023-01-15" },
  { id: 2, sourceName: "USAID", createAt: "2023-02-10", updatedAt: "2023-02-10" },
  { id: 3, sourceName: "European Union", createAt: "2023-03-05", updatedAt: "2023-03-05" },
  { id: 4, sourceName: "Bill & Melinda Gates Foundation", createAt: "2023-04-20", updatedAt: "2023-04-20" },
  { id: 5, sourceName: "Government of Rwanda", createAt: "2023-05-12", updatedAt: "2023-05-12" },
]

export default function FundingSourcePage() {
  const [fundingSources, setFundingSources] = useState(mockFundingSources)
  const [loading, setLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingSource, setEditingSource] = useState<any>(null)
  const [sourceName, setSourceName] = useState("")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const columns = [
    { key: "sourceName", label: "Source Name" },
    {
      key: "createAt",
      label: "Created At",
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: "updatedAt",
      label: "Updated At",
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    try {
      if (editingSource) {
        // Update existing source
        const updatedSources = fundingSources.map((source) =>
          source.id === editingSource.id ? { ...source, sourceName, updatedAt: new Date().toISOString() } : source,
        )
        setFundingSources(updatedSources)
        setSuccess("Funding source updated successfully")
      } else {
        // Create new source
        const newSource = {
          id: Math.max(...fundingSources.map((s) => s.id)) + 1,
          sourceName,
          createAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
        setFundingSources([...fundingSources, newSource])
        setSuccess("Funding source created successfully")
      }

      setDialogOpen(false)
      setSourceName("")
      setEditingSource(null)
    } catch (err) {
      setError("Failed to save funding source")
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (source: any) => {
    setEditingSource(source)
    setSourceName(source.sourceName)
    setDialogOpen(true)
  }

  const handleDelete = async (source: any) => {
    if (confirm("Are you sure you want to delete this funding source?")) {
      setFundingSources(fundingSources.filter((s) => s.id !== source.id))
      setSuccess("Funding source deleted successfully")
    }
  }

  const resetForm = () => {
    setSourceName("")
    setEditingSource(null)
    setError("")
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Funding Sources</h2>
        <Dialog
          open={dialogOpen}
          onOpenChange={(open) => {
            setDialogOpen(open)
            if (!open) resetForm()
          }}
        >
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Funding Source
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>{editingSource ? "Edit Funding Source" : "Add Funding Source"}</DialogTitle>
                <DialogDescription>
                  {editingSource ? "Update the funding source details." : "Create a new funding source."}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="sourceName">Source Name</Label>
                  <Input
                    id="sourceName"
                    value={sourceName}
                    onChange={(e) => setSourceName(e.target.value)}
                    placeholder="Enter funding source name"
                    required
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingSource ? "Updating..." : "Creating..."}
                    </>
                  ) : editingSource ? (
                    "Update"
                  ) : (
                    "Create"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <DataTable
        data={fundingSources}
        columns={columns}
        searchKey="sourceName"
        onEdit={handleEdit}
        onDelete={handleDelete}
        searchPlaceholder="Search funding sources..."
      />
    </div>
  )
}
