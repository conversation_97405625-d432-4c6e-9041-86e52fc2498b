"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/input-categories/page",{

/***/ "(app-pages-browser)/./components/ui/tree-view.tsx":
/*!*************************************!*\
  !*** ./components/ui/tree-view.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeView: () => (/* binding */ TreeView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,MoreHorizontal,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ TreeView auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst TreeNodeComponent = (param)=>{\n    let { node, level, onEdit, onDelete, onAddChild, nameKey, expandedNodes, onToggleExpand } = param;\n    var _node_children;\n    _s();\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = expandedNodes.has(node.id);\n    const handleToggle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TreeNodeComponent.useCallback[handleToggle]\": ()=>{\n            if (hasChildren) {\n                onToggleExpand(node.id);\n            }\n        }\n    }[\"TreeNodeComponent.useCallback[handleToggle]\"], [\n        hasChildren,\n        node.id,\n        onToggleExpand\n    ]);\n    const paddingLeft = level * 20;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"select-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center py-2 px-2 hover:bg-gray-50 rounded-md group relative transition-colors duration-150\",\n                style: {\n                    paddingLeft: \"\".concat(paddingLeft, \"px\")\n                },\n                children: [\n                    level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 bottom-0 flex\",\n                        children: [\n                            Array.from({\n                                length: level\n                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 flex justify-center\",\n                                    style: {\n                                        left: \"\".concat(i * 20, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-px bg-gray-200 h-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, i, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-6 border-l border-b border-gray-200 rounded-bl\",\n                                style: {\n                                    left: \"\".concat((level - 1) * 20, \"px\"),\n                                    top: '50%',\n                                    transform: 'translateY(-50%)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleToggle,\n                                className: \"flex items-center justify-center w-6 h-6 mr-2 hover:bg-gray-200 rounded transition-colors duration-150\",\n                                disabled: !hasChildren,\n                                children: hasChildren ? isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mr-2\",\n                                children: hasChildren ? isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 bg-gray-300 rounded-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-sm truncate\",\n                                                children: node[nameKey] || node.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs px-1.5 py-0.5\",\n                                                        children: [\n                                                            \"L\",\n                                                            level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-xs px-1.5 py-0.5\",\n                                                        children: ((_node_children = node.children) === null || _node_children === void 0 ? void 0 : _node_children.length) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 truncate mt-0.5\",\n                                        children: node.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150\",\n                        children: [\n                            onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 w-7 p-0 hover:bg-blue-100\",\n                                onClick: ()=>onEdit(node),\n                                title: \"Edit\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-3 h-3 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            onAddChild && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 w-7 p-0 hover:bg-green-100\",\n                                onClick: ()=>onAddChild(node),\n                                title: \"Add Child\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-3 h-3 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"h-7 w-7 p-0 hover:bg-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"More actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-3 h-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-48\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                                                children: [\n                                                    'Actions for \"',\n                                                    node[nameKey] || node.name,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>onEdit(node),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Edit Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            onAddChild && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>onAddChild(node),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Sub-Domain\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: handleToggle,\n                                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Collapse\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Expand\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                        onClick: ()=>onDelete(node),\n                                                        className: \"text-red-600 focus:text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-2 border-l border-gray-200\",\n                children: node.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: index === node.children.length - 1 ? \"border-l-transparent\" : \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TreeNodeComponent, {\n                            node: child,\n                            level: level + 1,\n                            onEdit: onEdit,\n                            onDelete: onDelete,\n                            onAddChild: onAddChild,\n                            nameKey: nameKey,\n                            expandedNodes: expandedNodes,\n                            onToggleExpand: onToggleExpand\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 15\n                        }, undefined)\n                    }, child.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TreeNodeComponent, \"ZlkaYGR3P75MlG1Y6zRKbNQ3VWg=\");\n_c = TreeNodeComponent;\nconst TreeView = (param)=>{\n    let { data, onEdit, onDelete, onAddChild, nameKey = \"name\", className = \"\", defaultExpandAll = true, expandedNodes: controlledExpandedNodes, onExpandedChange } = param;\n    _s1();\n    // Internal state for expanded nodes\n    const [internalExpandedNodes, setInternalExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"TreeView.useState\": ()=>{\n            if (defaultExpandAll) {\n                const getAllNodeIds = {\n                    \"TreeView.useState.getAllNodeIds\": (nodes)=>{\n                        const ids = [];\n                        nodes.forEach({\n                            \"TreeView.useState.getAllNodeIds\": (node)=>{\n                                ids.push(node.id);\n                                if (node.children) {\n                                    ids.push(...getAllNodeIds(node.children));\n                                }\n                            }\n                        }[\"TreeView.useState.getAllNodeIds\"]);\n                        return ids;\n                    }\n                }[\"TreeView.useState.getAllNodeIds\"];\n                return new Set(getAllNodeIds(data));\n            }\n            return new Set();\n        }\n    }[\"TreeView.useState\"]);\n    // Use controlled or internal state\n    const expandedNodes = controlledExpandedNodes || internalExpandedNodes;\n    const setExpandedNodes = onExpandedChange || setInternalExpandedNodes;\n    const handleToggleExpand = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TreeView.useCallback[handleToggleExpand]\": (nodeId)=>{\n            const newExpandedNodes = new Set(expandedNodes);\n            if (newExpandedNodes.has(nodeId)) {\n                newExpandedNodes.delete(nodeId);\n            } else {\n                newExpandedNodes.add(nodeId);\n            }\n            setExpandedNodes(newExpandedNodes);\n        }\n    }[\"TreeView.useCallback[handleToggleExpand]\"], [\n        expandedNodes,\n        setExpandedNodes\n    ]);\n    // Memoize the tree rendering for performance\n    const renderTree = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TreeView.useMemo[renderTree]\": ()=>{\n            if (!data || data.length === 0) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-gray-500 \".concat(className),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_MoreHorizontal_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-8 h-8 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"No items found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, undefined);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1 \".concat(className),\n                children: data.map({\n                    \"TreeView.useMemo[renderTree]\": (node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TreeNodeComponent, {\n                            node: node,\n                            level: 0,\n                            onEdit: onEdit,\n                            onDelete: onDelete,\n                            onAddChild: onAddChild,\n                            nameKey: nameKey,\n                            expandedNodes: expandedNodes,\n                            onToggleExpand: handleToggleExpand\n                        }, node.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, undefined)\n                }[\"TreeView.useMemo[renderTree]\"])\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined);\n        }\n    }[\"TreeView.useMemo[renderTree]\"], [\n        data,\n        className,\n        onEdit,\n        onDelete,\n        onAddChild,\n        nameKey,\n        expandedNodes,\n        handleToggleExpand\n    ]);\n    return renderTree;\n};\n_s1(TreeView, \"LlpYBUQ0eEmXQUIO+BH+fs+Znhs=\");\n_c1 = TreeView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TreeNodeComponent\");\n$RefreshReg$(_c1, \"TreeView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/tree-view.tsx\n"));

/***/ })

});