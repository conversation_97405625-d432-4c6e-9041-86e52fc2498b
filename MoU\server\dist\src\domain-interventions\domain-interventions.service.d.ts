import { PrismaService } from '../prisma/prisma.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';
export declare class DomainInterventionsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createDomainInterventionDto: CreateDomainInterventionDto, userId: string): Promise<{
        parent: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        children: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        }[];
    } & {
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    findAll(): Promise<({
        parent: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        children: ({
            children: ({
                children: {
                    id: number;
                    domainName: string;
                    description: string | null;
                    parentId: number | null;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                }[];
            } & {
                id: number;
                domainName: string;
                description: string | null;
                parentId: number | null;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            })[];
        } & {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        })[];
    } & {
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    })[]>;
    findTree(): Promise<any[]>;
    findOne(id: number): Promise<{
        children: any[];
        parent: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, userId: string): Promise<{
        parent: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        children: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        }[];
    } & {
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: number, userId: string): Promise<{
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    private checkCircularReference;
}
