"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, MoreHorizontal, Search, Edit, Trash2 } from "lucide-react"
import { masterDataService, type Currency } from "@/lib/services/master-data.service"

export default function CurrencyPage() {
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [formLoading, setFormLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingCurrency, setEditingCurrency] = useState<Currency | null>(null)
  const [currencyCode, setCurrencyCode] = useState("")
  const [currencyName, setCurrencyName] = useState("")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    loadCurrencies()
  }, [])

  const loadCurrencies = async () => {
    try {
      setLoading(true)
      const data = await masterDataService.getCurrencies()
      setCurrencies(data)
    } catch (error) {
      console.error("Failed to load currencies:", error)
      setError("Failed to load currencies")
    } finally {
      setLoading(false)
    }
  }

  const filteredCurrencies = currencies.filter((currency) =>
    currency.currencyCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    currency.currencyName.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormLoading(true)
    setError("")
    setSuccess("")

    try {
      if (editingCurrency) {
        // Update existing currency
        await masterDataService.updateCurrency(editingCurrency.id, { currencyCode, currencyName })
        setSuccess("Currency updated successfully")
      } else {
        // Create new currency
        await masterDataService.createCurrency({ currencyCode, currencyName })
        setSuccess("Currency created successfully")
      }

      // Reload the data to get the latest from server
      await loadCurrencies()
      setDialogOpen(false)
      setCurrencyCode("")
      setCurrencyName("")
      setEditingCurrency(null)
    } catch (err: any) {
      console.error("Failed to save currency:", err)
      setError(err.response?.data?.message || "Failed to save currency")
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (currency: Currency) => {
    setEditingCurrency(currency)
    setCurrencyCode(currency.currencyCode)
    setCurrencyName(currency.currencyName)
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm("Are you sure you want to delete this currency?")) {
      try {
        await masterDataService.deleteCurrency(id)
        setSuccess("Currency deleted successfully")
        // Reload the data to get the latest from server
        await loadCurrencies()
      } catch (err: any) {
        console.error("Failed to delete currency:", err)
        setError(err.response?.data?.message || "Failed to delete currency")
      }
    }
  }

  const resetForm = () => {
    setCurrencyCode("")
    setCurrencyName("")
    setEditingCurrency(null)
    setError("")
    setSuccess("")
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Currencies</h2>
        <Dialog
          open={dialogOpen}
          onOpenChange={(open) => {
            setDialogOpen(open)
            if (!open) resetForm()
          }}
        >
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Currency
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>{editingCurrency ? "Edit Currency" : "Add Currency"}</DialogTitle>
                <DialogDescription>
                  {editingCurrency ? "Update the currency details." : "Create a new currency."}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="currencyCode">Currency Code</Label>
                  <Input
                    id="currencyCode"
                    value={currencyCode}
                    onChange={(e) => setCurrencyCode(e.target.value.toUpperCase())}
                    placeholder="Enter currency code (e.g., USD, EUR)"
                    maxLength={3}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currencyName">Currency Name</Label>
                  <Input
                    id="currencyName"
                    value={currencyName}
                    onChange={(e) => setCurrencyName(e.target.value)}
                    placeholder="Enter currency name (e.g., US Dollar, Euro)"
                    required
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={formLoading}>
                  {formLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingCurrency ? "Updating..." : "Creating..."}
                    </>
                  ) : editingCurrency ? (
                    "Update"
                  ) : (
                    "Create"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search currencies..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Currency Code</TableHead>
              <TableHead>Currency Name</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Updated At</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                  <p className="mt-2 text-muted-foreground">Loading currencies...</p>
                </TableCell>
              </TableRow>
            ) : filteredCurrencies.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <p className="text-muted-foreground">
                    {searchTerm ? "No currencies found matching your search." : "No currencies found."}
                  </p>
                </TableCell>
              </TableRow>
            ) : (
              filteredCurrencies.map((currency) => (
                <TableRow key={currency.id}>
                  <TableCell className="font-medium font-mono">{currency.currencyCode}</TableCell>
                  <TableCell>{currency.currencyName}</TableCell>
                  <TableCell>{new Date(currency.createAt).toLocaleDateString()}</TableCell>
                  <TableCell>{new Date(currency.updatedAt).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleEdit(currency)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(currency.id)} className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
