"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain-interventions/page",{

/***/ "(app-pages-browser)/./app/dashboard/domain-interventions/page.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/domain-interventions/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DomainInterventionsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Loader2,Network,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _components_ui_tree_view__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tree-view */ \"(app-pages-browser)/./components/ui/tree-view.tsx\");\n/* harmony import */ var _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/master-data.service */ \"(app-pages-browser)/./lib/services/master-data.service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DomainInterventionsPage() {\n    _s();\n    const [domains, setDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [treeData, setTreeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formLoading, setFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingDomain, setEditingDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [parentDomain, setParentDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Form state\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedParentId, setSelectedParentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DomainInterventionsPage.useEffect\": ()=>{\n            loadDomains();\n        }\n    }[\"DomainInterventionsPage.useEffect\"], []);\n    const loadDomains = async ()=>{\n        try {\n            setLoading(true);\n            const [domainsData, treeData] = await Promise.all([\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.getDomainInterventions(),\n                _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.getDomainInterventionsTree()\n            ]);\n            setDomains(domainsData);\n            setTreeData(treeData);\n        } catch (error) {\n            console.error(\"Failed to load domain interventions:\", error);\n            setError(\"Failed to load domain interventions\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetForm = ()=>{\n        setDomainName(\"\");\n        setDescription(\"\");\n        setSelectedParentId(\"none\");\n        setEditingDomain(null);\n        setParentDomain(null);\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormLoading(true);\n        setError(\"\");\n        try {\n            const domainData = {\n                domainName,\n                description: description || undefined,\n                parentId: selectedParentId && selectedParentId !== \"none\" ? parseInt(selectedParentId) : undefined\n            };\n            if (editingDomain) {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.updateDomainIntervention(editingDomain.id, domainData);\n                setSuccess(\"Domain intervention updated successfully\");\n            } else {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.createDomainIntervention(domainData);\n                setSuccess(\"Domain intervention created successfully\");\n            }\n            await loadDomains();\n            setDialogOpen(false);\n            resetForm();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to save domain intervention\");\n        } finally{\n            setFormLoading(false);\n        }\n    };\n    const handleEdit = (node)=>{\n        const domain = domains.find((d)=>d.id === node.id);\n        if (domain) {\n            setEditingDomain(domain);\n            setDomainName(domain.domainName);\n            setDescription(domain.description || \"\");\n            setSelectedParentId(domain.parentId ? domain.parentId.toString() : \"none\");\n            setDialogOpen(true);\n        }\n    };\n    const handleDelete = async (node)=>{\n        if (confirm(\"Are you sure you want to delete this domain intervention?\")) {\n            try {\n                await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_11__.masterDataService.deleteDomainIntervention(node.id);\n                setSuccess(\"Domain intervention deleted successfully\");\n                await loadDomains();\n            } catch (error) {\n                var _error_response_data, _error_response;\n                setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete domain intervention\");\n            }\n        }\n    };\n    const handleAddChild = (parentNode)=>{\n        const parent = domains.find((d)=>d.id === parentNode.id);\n        if (parent) {\n            setParentDomain(parent);\n            setSelectedParentId(parent.id.toString());\n            setDialogOpen(true);\n        }\n    };\n    const openCreateDialog = ()=>{\n        resetForm();\n        setDialogOpen(true);\n    };\n    // Convert domains to tree nodes\n    const convertToTreeNodes = (domains)=>{\n        return domains.map((domain)=>({\n                id: domain.id,\n                name: domain.domainName,\n                description: domain.description,\n                parentId: domain.parentId,\n                children: domain.children ? convertToTreeNodes(domain.children) : []\n            }));\n    };\n    // Filter tree data based on search term\n    const filterTreeData = (nodes, searchTerm)=>{\n        if (!searchTerm) return nodes;\n        const filtered = [];\n        const searchInNode = (node)=>{\n            const matchesSearch = node.domainName.toLowerCase().includes(searchTerm.toLowerCase()) || node.description && node.description.toLowerCase().includes(searchTerm.toLowerCase());\n            const filteredChildren = node.children ? filterTreeData(node.children, searchTerm) : [];\n            const hasMatchingChildren = filteredChildren.length > 0;\n            if (matchesSearch || hasMatchingChildren) {\n                filtered.push({\n                    ...node,\n                    children: filteredChildren\n                });\n                return true;\n            }\n            return false;\n        };\n        nodes.forEach(searchInNode);\n        return filtered;\n    };\n    const filteredTreeData = filterTreeData(treeData, searchTerm);\n    const treeNodes = convertToTreeNodes(filteredTreeData);\n    // Calculate statistics\n    const getMaxDepth = function(nodes) {\n        let currentDepth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (!nodes || nodes.length === 0) return currentDepth;\n        return Math.max(...nodes.map((node)=>getMaxDepth(node.children || [], currentDepth + 1)));\n    };\n    const getTotalNodes = (nodes)=>{\n        return nodes.reduce((total, node)=>total + 1 + getTotalNodes(node.children || []), 0);\n    };\n    const maxDepth = getMaxDepth(treeNodes);\n    const totalNodes = getTotalNodes(treeNodes);\n    const totalFilteredNodes = searchTerm ? getTotalNodes(treeNodes) : domains.length;\n    // Expand/Collapse all functionality\n    const expandAll = ()=>{\n        const getAllNodeIds = (nodes)=>{\n            const ids = [];\n            nodes.forEach((node)=>{\n                ids.push(node.id);\n                if (node.children) {\n                    ids.push(...getAllNodeIds(node.children));\n                }\n            });\n            return ids;\n        };\n        setExpandedNodes(new Set(getAllNodeIds(treeNodes)));\n    };\n    const collapseAll = ()=>{\n        setExpandedNodes(new Set());\n    };\n    // Filter domains for parent selection (exclude current domain and its descendants)\n    const getSelectableParents = ()=>{\n        if (!editingDomain) return domains // All domains available for new items\n        ;\n        const excludeIds = new Set();\n        // Add current domain\n        excludeIds.add(editingDomain.id);\n        // Add all descendants recursively\n        const addDescendants = (parentId)=>{\n            domains.filter((d)=>d.parentId === parentId).forEach((child)=>{\n                excludeIds.add(child.id);\n                addDescendants(child.id);\n            });\n        };\n        addDescendants(editingDomain.id);\n        return domains.filter((d)=>!excludeIds.has(d.id));\n    };\n    // Create hierarchical display names for parent selection\n    const getHierarchicalDisplayName = (domain)=>{\n        const getPath = (d)=>{\n            if (!d.parentId) return [\n                d.domainName\n            ];\n            const parent = domains.find((p)=>p.id === d.parentId);\n            if (!parent) return [\n                d.domainName\n            ];\n            return [\n                ...getPath(parent),\n                d.domainName\n            ];\n        };\n        const path = getPath(domain);\n        return path.length > 1 ? path.join(' → ') : path[0];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Domain Interventions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: dialogOpen,\n                        onOpenChange: setDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: openCreateDialog,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Add Domain Intervention\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                className: \"sm:max-w-[500px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                children: editingDomain ? \"Edit Domain Intervention\" : parentDomain ? 'Add Sub-Domain to \"'.concat(parentDomain.domainName, '\"') : \"Create New Domain Intervention\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                children: editingDomain ? \"Update domain intervention information.\" : \"Add a new domain intervention to the system.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4 py-4\",\n                                                children: [\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                                        variant: \"destructive\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"domainName\",\n                                                                children: \"Domain Name *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"domainName\",\n                                                                value: domainName,\n                                                                onChange: (e)=>setDomainName(e.target.value),\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"description\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                id: \"description\",\n                                                                value: description,\n                                                                onChange: (e)=>setDescription(e.target.value),\n                                                                rows: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"parentId\",\n                                                                children: \"Parent Domain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                value: selectedParentId,\n                                                                onValueChange: setSelectedParentId,\n                                                                disabled: !!parentDomain,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                            placeholder: parentDomain ? parentDomain.domainName : \"Select parent domain (optional)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                value: \"none\",\n                                                                                children: \"No Parent (Root Domain)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            getSelectableParents().sort((a, b)=>{\n                                                                                // Sort by hierarchy depth first, then by name\n                                                                                const aDepth = getHierarchicalDisplayName(a).split(' → ').length;\n                                                                                const bDepth = getHierarchicalDisplayName(b).split(' → ').length;\n                                                                                if (aDepth !== bDepth) return aDepth - bDepth;\n                                                                                return getHierarchicalDisplayName(a).localeCompare(getHierarchicalDisplayName(b));\n                                                                            }).map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: domain.id.toString(),\n                                                                                    children: getHierarchicalDisplayName(domain)\n                                                                                }, domain.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                                    lineNumber: 322,\n                                                                                    columnNumber: 27\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"submit\",\n                                                    disabled: formLoading,\n                                                    children: formLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            editingDomain ? \"Updating...\" : \"Creating...\"\n                                                        ]\n                                                    }, void 0, true) : editingDomain ? \"Update Domain\" : \"Create Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                className: \"bg-green-50 text-green-700 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 350,\n                columnNumber: 9\n            }, this),\n            error && !dialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                placeholder: \"Search domain interventions...\",\n                                className: \"pl-8\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: expandAll,\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Expand All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: collapseAll,\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Collapse All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Loader2_Network_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Domain Hierarchy\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                children: \"Manage domain interventions with unlimited nesting. Create complex hierarchical structures for organizing interventions.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 text-sm text-muted-foreground mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Total: \",\n                                            searchTerm ? totalFilteredNodes : domains.length,\n                                            \" domains\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, this),\n                                    maxDepth > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Max depth: \",\n                                            maxDepth,\n                                            \" levels\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 34\n                                    }, this),\n                                    searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Showing: \",\n                                            totalNodes,\n                                            \" matches\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tree_view__WEBPACK_IMPORTED_MODULE_10__.TreeView, {\n                            data: treeNodes,\n                            onEdit: handleEdit,\n                            onDelete: handleDelete,\n                            onAddChild: handleAddChild,\n                            nameKey: \"name\",\n                            className: \"border rounded-lg p-4 bg-gray-50\",\n                            defaultExpandAll: false,\n                            expandedNodes: expandedNodes,\n                            onExpandedChange: setExpandedNodes\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n                lineNumber: 398,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\domain-interventions\\\\page.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainInterventionsPage, \"p6u1WhUCwVO+L1DDZQ8iEpy9td8=\");\n_c = DomainInterventionsPage;\nvar _c;\n$RefreshReg$(_c, \"DomainInterventionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/domain-interventions/page.tsx\n"));

/***/ })

});