"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/input-categories/page",{

/***/ "(app-pages-browser)/./components/ui/tree-view.tsx":
/*!*************************************!*\
  !*** ./components/ui/tree-view.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeView: () => (/* binding */ TreeView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ TreeView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst TreeNodeComponent = (param)=>{\n    let { node, level, onEdit, onDelete, onAddChild, nameKey } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const hasChildren = node.children && node.children.length > 0;\n    const handleToggle = ()=>{\n        if (hasChildren) {\n            setIsExpanded(!isExpanded);\n        }\n    };\n    const paddingLeft = level * 20;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"select-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center py-2 px-2 hover:bg-gray-50 rounded-md group relative\",\n                style: {\n                    paddingLeft: \"\".concat(paddingLeft, \"px\")\n                },\n                children: [\n                    level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 bottom-0 flex\",\n                        children: [\n                            Array.from({\n                                length: level\n                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 flex justify-center\",\n                                    style: {\n                                        left: \"\".concat(i * 20, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-px bg-gray-200 h-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, i, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-6 border-l border-b border-gray-200 rounded-bl\",\n                                style: {\n                                    left: \"\".concat((level - 1) * 20, \"px\"),\n                                    top: '50%',\n                                    transform: 'translateY(-50%)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center flex-1 min-w-0\",\n                        children: [\n                            hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleToggle,\n                                className: \"flex items-center justify-center w-4 h-4 mr-2 hover:bg-gray-200 rounded\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-sm truncate\",\n                                                children: node[nameKey] || node.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full\",\n                                                children: [\n                                                    \"L\",\n                                                    level\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    node.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 truncate\",\n                                        children: node.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Open menu\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-gray-400 rounded-full mx-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-gray-400 rounded-full mx-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-gray-400 rounded-full mx-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        onAddChild && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                            onClick: ()=>onAddChild(node),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Add Child\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                            onClick: ()=>onEdit(node),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                            onClick: ()=>onDelete(node),\n                                            className: \"text-red-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Delete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: node.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TreeNodeComponent, {\n                        node: child,\n                        level: level + 1,\n                        onEdit: onEdit,\n                        onDelete: onDelete,\n                        onAddChild: onAddChild,\n                        nameKey: nameKey\n                    }, child.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TreeNodeComponent, \"MzqrZ0LJxgqPa6EOF1Vxw0pgYA4=\");\n_c = TreeNodeComponent;\nconst TreeView = (param)=>{\n    let { data, onEdit, onDelete, onAddChild, nameKey = \"name\", className = \"\" } = param;\n    if (!data || data.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-gray-500 \".concat(className),\n            children: \"No items found\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1 \".concat(className),\n        children: data.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TreeNodeComponent, {\n                node: node,\n                level: 0,\n                onEdit: onEdit,\n                onDelete: onDelete,\n                onAddChild: onAddChild,\n                nameKey: nameKey\n            }, node.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\ui\\\\tree-view.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TreeView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TreeNodeComponent\");\n$RefreshReg$(_c1, \"TreeView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/tree-view.tsx\n"));

/***/ })

});