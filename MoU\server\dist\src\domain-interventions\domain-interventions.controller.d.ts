import { DomainInterventionsService } from './domain-interventions.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';
export declare class DomainInterventionsController {
    private readonly domainInterventionsService;
    constructor(domainInterventionsService: DomainInterventionsService);
    create(createDomainInterventionDto: CreateDomainInterventionDto, req: any): Promise<{
        parent: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        children: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        }[];
    } & {
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    findAll(): Promise<({
        parent: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        children: ({
            children: ({
                children: {
                    id: number;
                    domainName: string;
                    description: string | null;
                    parentId: number | null;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                }[];
            } & {
                id: number;
                domainName: string;
                description: string | null;
                parentId: number | null;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            })[];
        } & {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        })[];
    } & {
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    })[]>;
    findTree(): Promise<any[]>;
    findOne(id: number): Promise<{
        children: any[];
        parent: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, req: any): Promise<{
        parent: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        children: {
            id: number;
            domainName: string;
            description: string | null;
            parentId: number | null;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        }[];
    } & {
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: number, req: any): Promise<{
        id: number;
        domainName: string;
        description: string | null;
        parentId: number | null;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
}
