"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./components/app-sidebar.tsx":
/*!************************************!*\
  !*** ./components/app-sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/heart-pulse.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-input.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/landmark.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Briefcase,Building,ChevronDown,Coins,CreditCard,DollarSign,FileInputIcon,HeartPulse,Home,Landmark,LogOut,Menu,Settings,UserCircle,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AppSidebar() {\n    var _user_firstName, _user_lastName;\n    _s();\n    const { user, logout } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isActive = (path)=>{\n        return pathname === path || pathname.startsWith(\"\".concat(path, \"/\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 px-4 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarTrigger, {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    src: \"/logo.png\",\n                                    alt: \"MoU Management System\",\n                                    width: 30,\n                                    height: 30\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold\",\n                                    children: \"MoH MoU System\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroup, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroupLabel, {\n                                children: \"User Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroupContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                asChild: true,\n                                                isActive: isActive(\"/dashboard\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Home\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        (user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                asChild: true,\n                                                isActive: isActive(\"/dashboard/admin\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/admin\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Admin Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                asChild: true,\n                                                isActive: isActive(\"/dashboard/users\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/users\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                asChild: true,\n                                                isActive: isActive(\"/dashboard/profile\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/profile\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"My Profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroup, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.Collapsible, {\n                            className: \"group/collapsible\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroupLabel, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.CollapsibleTrigger, {\n                                        className: \"flex w-full items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Organizational Setup\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.CollapsibleContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroupContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/domain-interventions\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/domain-interventions\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 123,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Domain Interventions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 124,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/organization-types\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/organization-types\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Organization Types\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/healthcare-providers\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/healthcare-providers\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Health Care Provider\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 140,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/input-categories\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/input-categories\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Input categories\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroup, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.Collapsible, {\n                            className: \"group/collapsible\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroupLabel, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.CollapsibleTrigger, {\n                                        className: \"flex w-full items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Financial Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_7__.CollapsibleContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarGroupContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/budget-types\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/budget-types\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Budget Types\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/funding-source\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/funding-source\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Funding Source\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/funding-unit\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/funding-unit\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Funding Unit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/financing-schemes\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/financing-schemes\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Financing Schemes\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/financing-agents\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/financing-agents\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Financing Agents\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                                        asChild: true,\n                                                        isActive: isActive(\"/dashboard/currency\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/dashboard/currency\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Currency\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-4 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                            src: \"/placeholder.svg\",\n                                            alt: user === null || user === void 0 ? void 0 : user.firstName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName[0],\n                                                user === null || user === void 0 ? void 0 : (_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName[0]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : user.firstName,\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: user === null || user === void 0 ? void 0 : user.role\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>logout(),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Briefcase_Building_ChevronDown_Coins_CreditCard_DollarSign_FileInputIcon_HeartPulse_Home_Landmark_LogOut_Menu_Settings_UserCircle_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"tHLzmZfHifuHte7SpZSOu+/8DHo=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/app-sidebar.tsx\n"));

/***/ })

});